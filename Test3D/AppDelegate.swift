//
//  AppDelegate.swift
//  Test3D
//
//  Created by 王雷 on 2/6/25.
//

import Cocoa
import MetalKit // 新增


class AppDelegate: NSObject, NSApplicationDelegate, NSWindowDelegate { // 遵守NSWindowDelegate协议

    var window: NSWindow?
    var windowController: NSWindowController?
    var mainViewController: ViewController?  // 保持 ViewController 实例


    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // 创建并显示主窗口
        createMainWindow()
        window?.makeKeyAndOrderFront(nil)
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        // Insert code here to tear down your application
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    // 当最后一个窗口关闭时，不退出应用程序
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> <PERSON><PERSON> {
        return false
    }

    // 当点击程序坞图标时重新显示窗口
    func applicationShouldHandleReopen(_ sender: NSApplication, hasVisibleWindows flag: Bool) -> Bool {
        print("应用程序要求重新打开，可见窗口：\(flag)")

        if !flag {
            // 由于设置了 isReleasedWhenClosed = false，窗口应该始终存在
            if let window = window {
                // 直接显示现有窗口
                window.makeKeyAndOrderFront(nil)
                NSApp.activate(ignoringOtherApps: true)
                print("显示现有窗口")
            } else {
                // 如果窗口不存在（不应该发生），则创建新窗口
                print("窗口不存在，创建新窗口")
                createMainWindow()
                showMainWindow()
            }
        }

        return true
    }

    // 显示主窗口的方法
    func showMainWindow() {
        if let window = window {
            // 显示并激活窗口
            window.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
        }
    }

    // 创建主窗口的方法（从 applicationDidFinishLaunching 中提取出来）
    func createMainWindow() {
        let newWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )

        // 设置窗口属性
        newWindow.title = "Test3D"
        newWindow.center()
        newWindow.isReleasedWhenClosed = false  // 关键：确保窗口关闭时不会被释放
        newWindow.delegate = self // 设置窗口代理为AppDelegate自身

        // 创建或复用 ViewController 实例
        if mainViewController == nil {
            mainViewController = ViewController()
        }
        newWindow.contentViewController = mainViewController

        // 创建窗口控制器
        let controller = NSWindowController(window: newWindow)

        // 保存引用
        self.window = newWindow
        self.windowController = controller
    }

    // MARK: - NSWindowDelegate
    func windowDidDeminiaturize(_ notification: Notification) {
        print("🚪 AppDelegate: Window didDeminiaturize.")
        // 当窗口从最小化状态恢复时，简化恢复逻辑
        if let vc = mainViewController {
            vc.resumeRendering()
        }
    }
    
    // 简化windowDidBecomeMain，只在真正需要时才执行
    func windowDidBecomeMain(_ notification: Notification) {
        print("🚪 AppDelegate: Window didBecomeMain.")
        // 只打印日志，不再自动触发恢复逻辑
        // 由ViewController的viewWillAppear处理恢复逻辑
    }

    // MARK: - 强制重建MTKView layer连接 (从ViewController迁移过来)
    private func forceRecreateMetalViewLayerAndRestartAnimation(for mtkView: MTKView, device: MTLDevice, delegate: NSObject & MTKViewDelegate) {
        let recreateStart = CACurrentMediaTime()
        print("🔧 AppDelegate: forceRecreateMetalViewLayerAndRestartAnimation called at \(recreateStart).")

        // 1. 确保MTKView配置正确
        mtkView.device = device
        if let mtkViewDelegate = delegate as? MTKViewDelegate {
            mtkView.delegate = mtkViewDelegate
            print("   forceRecreate: MTKView delegate设置完成")
        } else {
            print("⚠️ forceRecreate: Delegate is not MTKViewDelegate.")
            return
        }
        
        // 设置关键的渲染属性
        mtkView.depthStencilPixelFormat = .depth32Float
        mtkView.colorPixelFormat = .bgra8Unorm
        mtkView.sampleCount = 4
        mtkView.clearColor = MTLClearColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0)
        
        // 配置动画相关属性
        mtkView.enableSetNeedsDisplay = false  // 连续渲染模式
        mtkView.preferredFramesPerSecond = 60
        
        // 配置CAMetalLayer以获得最佳动画性能
        if let metalLayer = mtkView.layer as? CAMetalLayer {
            metalLayer.presentsWithTransaction = false  // 关键：避免事务阻塞
            metalLayer.displaySyncEnabled = true        // 启用垂直同步
            print("   forceRecreate: CAMetalLayer configured for smooth animation.")
        }

        // 2. 恢复MTKView渲染 - 不要在这里设置isPaused，让resumeRendering()处理
        print("   forceRecreate: Resuming MTKView rendering.")
        if let delegateVC = delegate as? ViewController {
            delegateVC.resumeRendering()
            print("   forceRecreate: MTKView rendering resumed via resumeRendering.")
        } else {
            print("   forceRecreate: Direct MTKView rendering resume.")
            mtkView.isPaused = false
        }
        
        print("✅ AppDelegate: MTKView configuration updated and animation restarted.")

        // 3. 强制立即渲染一帧
        DispatchQueue.main.async {
            print("🎬 forceRecreate: Explicitly requesting display update.")
            mtkView.setNeedsDisplay(mtkView.bounds)
        }
    }


}

