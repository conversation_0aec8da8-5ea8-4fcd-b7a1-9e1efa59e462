//
//  MathUtils.swift
//  Test3D
//
//  Created by 王雷 on 2/6/25.
//

import simd

extension simd_float4x4 {
    
    // 创建旋转矩阵（绕Y轴）
    init(rotationY angle: Float) {
        let c = cos(angle)
        let s = sin(angle)
        
        self.init(
            SIMD4<Float>(c, 0, s, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(-s, 0, c, 0),
            SIMD4<Float>(0, 0, 0, 1)
        )
    }
    
    // 创建透视投影矩阵
    init(perspectiveWithFovy fovy: Float, aspect: Float, nearZ: Float, farZ: Float) {
        let yScale = 1 / tan(fovy * 0.5)
        let xScale = yScale / aspect
        let zRange = farZ - nearZ
        let zScale = -(farZ + nearZ) / zRange
        let wzScale = -2 * farZ * nearZ / zRange
        
        self.init(
            SIMD4<Float>(xScale, 0, 0, 0),
            SIMD4<Float>(0, yScale, 0, 0),
            SIMD4<Float>(0, 0, zScale, -1),
            SIMD4<Float>(0, 0, wzScale, 0)
        )
    }
    
    // 创建视图矩阵（lookAt）
    init(lookAt eye: SIMD3<Float>, target: SIMD3<Float>, up: SIMD3<Float>) {
        let zAxis = normalize(eye - target)
        let xAxis = normalize(cross(up, zAxis))
        let yAxis = cross(zAxis, xAxis)
        
        self.init(
            SIMD4<Float>(xAxis.x, yAxis.x, zAxis.x, 0),
            SIMD4<Float>(xAxis.y, yAxis.y, zAxis.y, 0),
            SIMD4<Float>(xAxis.z, yAxis.z, zAxis.z, 0),
            SIMD4<Float>(-dot(xAxis, eye), -dot(yAxis, eye), -dot(zAxis, eye), 1)
        )
    }
    
    // 矩阵的逆
    var inverse: simd_float4x4 {
        return simd_inverse(self)
    }
    
    // 矩阵的转置
    var transpose: simd_float4x4 {
        return simd_transpose(self)
    }
}
