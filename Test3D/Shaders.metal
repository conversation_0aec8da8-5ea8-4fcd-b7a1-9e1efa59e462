//
//  Shaders.metal
//  Test3D
//
//  Created by 王雷 on 2/6/25.
//

#include <metal_stdlib>
using namespace metal;

// 顶点输入结构
struct VertexIn {
    float4 position [[attribute(0)]];
    float4 normal [[attribute(1)]];
    float4 texCoord [[attribute(2)]];
};

// 顶点输出结构
struct VertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 normal;
    float2 texCoord;
};

// 统一变量结构
struct Uniforms {
    float4x4 modelMatrix;
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
    float4x4 normalMatrix;
    float4 lightPosition;
    float4 cameraPosition;
    float time;
    float3 _padding; // 添加填充以匹配Swift结构
};

// 顶点着色器
vertex VertexOut vertexShader_Earth(VertexIn in [[stage_in]],
                                   constant Uniforms& uniforms [[buffer(1)]]) {
    VertexOut out;
    
    // 计算世界空间位置 - 使用float4输入位置的xyz部分
    float4 worldPosition = uniforms.modelMatrix * float4(in.position.xyz, 1.0);
    out.worldPosition = worldPosition.xyz;
    
    // 计算最终位置
    float4 viewPosition = uniforms.viewMatrix * worldPosition;
    out.position = uniforms.projectionMatrix * viewPosition;
    
    // 变换法线到世界空间 - 使用float4输入法线的xyz部分
    out.normal = normalize((uniforms.normalMatrix * float4(in.normal.xyz, 0.0)).xyz);
    
    // 传递纹理坐标 - 使用float4输入纹理坐标的xy部分
    out.texCoord = in.texCoord.xy;
    
    return out;
}

// 片段着色器 - Blinn-Phong光照模型
fragment float4 fragmentShader_Earth(VertexOut in [[stage_in]],
                                    texture2d<float> earthTexture [[texture(0)]],
                                    constant Uniforms& uniforms [[buffer(1)]]) {
    
    constexpr sampler textureSampler(mag_filter::linear,
                                   min_filter::linear,
                                   mip_filter::linear,
                                   address::repeat);
    
    // 使用地球纹理
    float3 baseColor = earthTexture.sample(textureSampler, in.texCoord).rgb;
    
    // 光照计算
    float3 normal = normalize(in.normal);
    float3 lightDir = normalize(uniforms.lightPosition.xyz - in.worldPosition);
    float3 viewDir = normalize(uniforms.cameraPosition.xyz - in.worldPosition);
    float3 halfwayDir = normalize(lightDir + viewDir);
    
    // 环境光
    float3 ambient = 0.15 * baseColor;
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    float3 diffuse = diff * baseColor;
    
    // 镜面反射 (Blinn-Phong)
    float spec = pow(max(dot(normal, halfwayDir), 0.0), 64.0);
    float3 specular = spec * float3(0.3, 0.3, 0.3);
    
    // 边缘光效果
    float fresnel = pow(1.0 - max(dot(normal, viewDir), 0.0), 2.0);
    float3 rimLight = fresnel * float3(0.1, 0.2, 0.4);
    
    // 组合最终颜色
    float3 finalColor = ambient + diffuse + specular + rimLight;
    
    return float4(finalColor, 1.0);
}
