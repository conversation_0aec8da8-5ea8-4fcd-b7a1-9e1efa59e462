//
//  ViewController.swift
//  Test3D
//
//  Created by 王雷 on 2/6/25.
//

import Cocoa
import Metal
import MetalKit
import simd

// 顶点结构
struct Vertex {
    var position: SIMD4<Float> // 统一为SIMD4<Float>
    var normal: SIMD4<Float>   // 统一为SIMD4<Float>
    var texCoord: SIMD4<Float> // 统一为SIMD4<Float>
}

// 统一变量结构 - 注意：为了与Metal shader对齐，使用SIMD4而不是SIMD3
struct Uniforms {
    var modelMatrix: simd_float4x4
    var viewMatrix: simd_float4x4
    var projectionMatrix: simd_float4x4
    var normalMatrix: simd_float4x4 // 在Swift中，Metal的float3x3对应simd_float3x3
    var lightPosition: SIMD4<Float>
    var cameraPosition: SIMD4<Float>
    var time: Float
    var _padding: SIMD3<Float> // 添加12字节填充，确保总大小为304字节
}

class ViewController: NSViewController, MTKViewDelegate { // 显式遵守 MTKViewDelegate 协议

    // Metal 相关属性
    internal var metalView: MTKView! // 改为 internal
    internal var device: MTLDevice!   // 改为 internal
    internal var commandQueue: MTLCommandQueue! // 改为 internal
    private var renderPipelineState: MTLRenderPipelineState!
    private var depthStencilState: MTLDepthStencilState!

    // 地球专用资源 - 独立的uniform buffer，不与其他对象共享
    private var earthVertexBuffer: MTLBuffer!
    private var earthIndexBuffer: MTLBuffer!
    private var earthUniformBuffer: MTLBuffer!  // 地球专用的uniform buffer
    private var earthTexture: MTLTexture!

    // 动画计时
    private var startTime: CFTimeInterval = 0

    // 几何数据
    private var vertices: [Vertex] = []
    private var indices: [UInt16] = []

    // 用于跟踪渲染状态
    private var isRenderingActive = false
    private var frameCount = 0  // 用于调试的帧计数器
    
    // CVDisplayLink 用于macOS的精确显示同步
    private var displayLink: CVDisplayLink?
    
    // 备用渲染计时器，当MTKView自动渲染失效时使用
    private var backupTimer: Timer?

    override func viewDidLoad() {
        super.viewDidLoad()

        print("🚀 ViewController.viewDidLoad() 开始初始化")
        
        // 初始化 Metal 渲染
        setupMetalRendering()
        generateEarthGeometry()
        createBuffers()
        setupRenderPipeline()
        loadEarthTexture()
        
        // 重要：设置MTKView为连续渲染模式
        metalView.enableSetNeedsDisplay = false  // 禁用手动setNeedsDisplay，使用自动渲染
        metalView.isPaused = false               // 确保不暂停
        metalView.preferredFramesPerSecond = 60  // 设置60fps
        
        // 配置CAMetalLayer以获得最佳动画性能
        if let metalLayer = metalView.layer as? CAMetalLayer {
            metalLayer.presentsWithTransaction = false  // 关键：避免阻塞动画
            metalLayer.displaySyncEnabled = true        // 垂直同步
        }
        
        startTime = CACurrentMediaTime()
        isRenderingActive = true
        
        print("✅ Metal 渲染设置完成，启用连续渲染模式")
        print("   - 顶点数量: \(vertices.count)")
        print("   - 索引数量: \(indices.count)")
        print("   - 开始时间: \(startTime)")
        print("   - MTKView isPaused: \(metalView.isPaused)")
        print("   - MTKView preferredFramesPerSecond: \(metalView.preferredFramesPerSecond)")
    }

    override func viewWillAppear() {
        super.viewWillAppear()
        print("📺 ViewController.viewWillAppear() - 视图即将显示")

        // 恢复渲染
        resumeRendering()
    }

    override func viewDidDisappear() {
        super.viewDidDisappear()
        print("📺 ViewController.viewDidDisappear() - 视图已隐藏")

        // 暂停渲染以节省资源
        pauseRendering()
    }

    override var representedObject: Any? {
        didSet {
        // Update the view, if already loaded.
        }
    }

    // MARK: - Metal 渲染相关方法

    private func setupMetalRendering() {
        // 创建 Metal 设备
        guard let device = MTLCreateSystemDefaultDevice() else {
            fatalError("Metal 不支持")
        }
        self.device = device

        // 创建 MTKView
        metalView = MTKView(frame: view.bounds, device: device)
        metalView.delegate = self
        metalView.sampleCount = 4  // 启用 MSAA 抗锯齿
        metalView.colorPixelFormat = .bgra8Unorm
        metalView.depthStencilPixelFormat = .depth32Float
        metalView.clearColor = MTLClearColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0)
        
        view.addSubview(metalView)
        metalView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            metalView.topAnchor.constraint(equalTo: view.topAnchor),
            metalView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            metalView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            metalView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])

        // 创建命令队列
        commandQueue = device.makeCommandQueue()
        
        print("基础Metal渲染环境设置完成")
    }

    internal func resumeRendering() {
        guard !isRenderingActive else { return }
        NSLog("🔧 恢复 Metal 渲染")
        isRenderingActive = true

        // 计算新的startTime，使旋转看起来连续
        // 保存旧的时间差，确保旋转角度连续
        let currentMediaTime = CACurrentMediaTime()
        let timeOffset = currentMediaTime - startTime
        
        // 如果之前渲染过（frameCount > 0），保持旋转连续性
        // 否则重置开始时间（首次渲染）
        if frameCount > 0 {
            NSLog("🔄 保持旋转连续性，旧时间差: \(timeOffset)")
            // 调整startTime使得计算出的currentTime保持连续
            startTime = currentMediaTime - timeOffset
        } else {
            startTime = currentMediaTime
        }
        
        // 🔑 关键修复：完全重建MTKView的渲染状态
        NSLog("🔧 完全重建MTKView渲染状态")
        
        // 1. 强制暂停并重置
        metalView.isPaused = true
        metalView.delegate = nil
        
        // 2. 等待一个渲染帧，确保暂停生效
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 3. 重新配置所有关键属性
            self.metalView.device = self.device  // 重新设置device
            self.metalView.delegate = self       // 重新设置delegate
            self.metalView.isPaused = false
            self.metalView.enableSetNeedsDisplay = false  // 连续渲染模式
            self.metalView.preferredFramesPerSecond = 60
            
            // 4. 强制重新配置CAMetalLayer
            if let metalLayer = self.metalView.layer as? CAMetalLayer {
                metalLayer.device = self.device  // 重新设置device
                metalLayer.presentsWithTransaction = false
                metalLayer.displaySyncEnabled = true
                metalLayer.allowsNextDrawableTimeout = false  // 防止渲染超时
                NSLog("✅ CAMetalLayer完全重新配置")
            }
            
            // 5. 多次触发draw来强制启动渲染循环 + 强制Timer备份机制
            NSLog("🎬 启动强化版渲染循环重启机制")
            
            // 方法A: 密集draw调用
            for i in 0..<10 {  // 增加到10次
                DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.016) { [weak self] in
                    guard let self = self else { return }
                    self.metalView.setNeedsDisplay(self.metalView.bounds)
                    self.metalView.draw()
                    
                    // 每次都强制更新delegate
                    if self.metalView.delegate == nil {
                        self.metalView.delegate = self
                    }
                    
                    if i == 0 {
                        NSLog("🎬 第\(i+1)次强制draw调用")
                    }
                }
            }
            
            // 简化渲染机制：直接使用备用计时器作为主要机制，避免多次切换
            // 根据日志分析，备用计时器提供了最稳定的渲染性能
            NSLog("🎬 简化渲染：直接启动备用计时器作为主要渲染机制")
            
            // 确保MTKView配置正确
            self.metalView.isPaused = false
            self.metalView.enableSetNeedsDisplay = false
            
            // 立即启动备用计时器，不再使用CVDisplayLink或尝试MTKView自动渲染
            self.startBackupTimer()
            
            // 仍保留验证步骤，但简化为单一检查点
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                NSLog("🔍 验证备用计时器渲染状态:")
                NSLog("   - isPaused: \(self.metalView.isPaused)")
                NSLog("   - enableSetNeedsDisplay: \(self.metalView.enableSetNeedsDisplay)")
                NSLog("   - delegate: \(self.metalView.delegate != nil ? "✅" : "❌")")
                NSLog("   - backupTimer: \(self.backupTimer != nil ? "✅" : "❌")")
            }
        }
        
        NSLog("MTKView 渲染已恢复，startTime 重置为 \(startTime)")
        NSLog("   - isPaused: \(metalView.isPaused)")
        NSLog("   - enableSetNeedsDisplay: \(metalView.enableSetNeedsDisplay)")
        NSLog("   - preferredFramesPerSecond: \(metalView.preferredFramesPerSecond)")
        NSLog("   - delegate: \(metalView.delegate != nil ? "✅" : "❌")")
        
        // 2秒后再次验证delegate状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            guard let self = self else { return }
            NSLog("🔍 2秒后delegate状态检查:")
            NSLog("   - delegate: \(self.metalView.delegate != nil ? "✅" : "❌")")
            NSLog("   - isPaused: \(self.metalView.isPaused)")
            if self.metalView.delegate == nil {
                NSLog("⚠️ Delegate再次丢失，重新设置")
                self.metalView.delegate = self
            }
        }
    }

    internal func pauseRendering() {
        guard isRenderingActive else { return }
        print("暂停 Metal 渲染")
        isRenderingActive = false

        // 停止所有渲染机制
        stopCVDisplayLink()
        stopBackupTimer()
        
        // 暂停MTKView渲染
        metalView.isPaused = true
        
        print("MTKView 渲染已暂停")
    }    // MARK: - Metal 设置方法

    private func setupRenderPipeline() {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }

        let vertexFunction = library.makeFunction(name: "vertexShader_Earth")
        let fragmentFunction = library.makeFunction(name: "fragmentShader_Earth")

        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.colorAttachments[0].pixelFormat = metalView.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = metalView.depthStencilPixelFormat
        // 统一使用 sampleCount
        pipelineDescriptor.sampleCount = metalView.sampleCount

        // 设置顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        // 位置属性
        vertexDescriptor.attributes[0].format = .float4
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0

        // 法线属性
        vertexDescriptor.attributes[1].format = .float4
        vertexDescriptor.attributes[1].offset = MemoryLayout<SIMD4<Float>>.stride
        vertexDescriptor.attributes[1].bufferIndex = 0

        // 纹理坐标属性
        vertexDescriptor.attributes[2].format = .float4
        vertexDescriptor.attributes[2].offset = MemoryLayout<SIMD4<Float>>.stride * 2
        vertexDescriptor.attributes[2].bufferIndex = 0

        // 缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<Vertex>.stride
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        pipelineDescriptor.vertexDescriptor = vertexDescriptor

        do {
            renderPipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("无法创建渲染管线状态: \(error)")
        }

        // 创建深度测试状态
        let depthStencilDescriptor = MTLDepthStencilDescriptor()
        depthStencilDescriptor.depthCompareFunction = .less
        depthStencilDescriptor.isDepthWriteEnabled = true
        depthStencilState = device.makeDepthStencilState(descriptor: depthStencilDescriptor)
    }

    private func generateEarthGeometry() {
        let latitudeBands = 60
        let longitudeBands = 60
        let radius: Float = 1.0

        vertices.removeAll()
        indices.removeAll()

        // 生成顶点
        for latNumber in 0...latitudeBands {
            let theta = Float(latNumber) * Float.pi / Float(latitudeBands)
            let sinTheta = sin(theta)
            let cosTheta = cos(theta)

            for longNumber in 0...longitudeBands {
                let phi = Float(longNumber) * 2 * Float.pi / Float(longitudeBands)
                let sinPhi = sin(phi)
                let cosPhi = cos(phi)

                let x = cosPhi * sinTheta
                let y = cosTheta
                let z = sinPhi * sinTheta

                let position = SIMD4<Float>(x * radius, y * radius, z * radius, 1.0)
                let normal = SIMD4<Float>(normalize(SIMD3<Float>(x, y, z)), 0.0) // 确保法线归一化并为SIMD4<Float>
                let texCoord = SIMD4<Float>(Float(longNumber) / Float(longitudeBands),
                                          Float(latNumber) / Float(latitudeBands), 0.0, 0.0) // 填充为SIMD4<Float>

                vertices.append(Vertex(position: position, normal: normal, texCoord: texCoord))
            }
        }

        // 生成索引
        for latNumber in 0..<latitudeBands {
            for longNumber in 0..<longitudeBands {
                let first = UInt16(latNumber * (longitudeBands + 1) + longNumber)
                let second = UInt16(first + UInt16(longitudeBands + 1))

                indices.append(first)
                indices.append(second)
                indices.append(first + 1)

                indices.append(second)
                indices.append(second + 1)
                indices.append(first + 1)
            }
        }
    }

    private func createBuffers() {
        // 创建地球专用的顶点缓冲区
        earthVertexBuffer = device.makeBuffer(bytes: vertices,
                                            length: vertices.count * MemoryLayout<Vertex>.size,
                                            options: [.storageModeShared])

        // 创建地球专用的索引缓冲区
        earthIndexBuffer = device.makeBuffer(bytes: indices,
                                           length: indices.count * MemoryLayout<UInt16>.size,
                                           options: [.storageModeShared])

        // 创建地球专用的统一变量缓冲区 - 独立使用，不与其他对象共享
        // 使用Shared存储模式以便CPU高效更新
        earthUniformBuffer = device.makeBuffer(length: MemoryLayout<Uniforms>.size, 
                                             options: [.storageModeShared])
        
        // 验证缓冲区创建成功
        guard earthVertexBuffer != nil, earthIndexBuffer != nil, earthUniformBuffer != nil else {
            fatalError("无法创建Metal缓冲区")
        }
        
        print("✅ Metal缓冲区创建成功: 顶点=\(vertices.count), 索引=\(indices.count), Uniform大小=\(MemoryLayout<Uniforms>.size)字节")
    }

    private func loadEarthTexture() {
        guard let url = Bundle.main.url(forResource: "earth", withExtension: "jpg") else {
            fatalError("找不到 earth.jpg 文件")
        }

        let textureLoader = MTKTextureLoader(device: device)
        do {
            earthTexture = try textureLoader.newTexture(URL: url, options: [
                .textureUsage: MTLTextureUsage.shaderRead.rawValue,
                .textureStorageMode: MTLStorageMode.`private`.rawValue
            ])
        } catch {
            fatalError("无法加载地球纹理: \(error)")
        }
    }

    private func updateUniforms(time: Float) {
        let rotationY = time * 1.0  // 地球自转速度

        // 模型矩阵 - 地球自转
        let modelMatrix = simd_float4x4(rotationY: rotationY)

        // 视图矩阵 - 摄像机位置
        let cameraPosition = SIMD3<Float>(0, 0, 3)
        let viewMatrix = simd_float4x4(lookAt: cameraPosition,
                                     target: SIMD3<Float>(0, 0, 0),
                                     up: SIMD3<Float>(0, 1, 0))

        // 投影矩阵
        let aspect = Float(metalView.bounds.width / metalView.bounds.height)
        let projectionMatrix = simd_float4x4(perspectiveWithFovy: Float.pi / 4,
                                           aspect: aspect,
                                           nearZ: 0.1,
                                           farZ: 100.0)

        // 法线矩阵
        let normalMatrix = modelMatrix.inverse.transpose

        // 光源位置
        let lightPosition = SIMD4<Float>(2, 2, 2, 1) // 转换为SIMD4<Float>

        let uniforms = Uniforms(
            modelMatrix: modelMatrix,
            viewMatrix: viewMatrix,
            projectionMatrix: projectionMatrix,
            normalMatrix: normalMatrix,
            lightPosition: lightPosition,
            cameraPosition: SIMD4<Float>(cameraPosition.x, cameraPosition.y, cameraPosition.z, 1), // 转换为SIMD4<Float>
            time: time,
            _padding: SIMD3<Float>(0, 0, 0) // 为 _padding 字段提供默认值
        )

        // 更新地球专用的uniform buffer - 使用更安全的内存访问方式
        let bufferPointer = earthUniformBuffer.contents().bindMemory(to: Uniforms.self, capacity: 1)
        bufferPointer.pointee = uniforms
        
        // 为Shared存储模式确保内存同步
        if earthUniformBuffer.storageMode == .shared {
            // 对于Shared模式，添加内存屏障确保写入完成
            _ = MemoryLayout<Uniforms>.size // 强制编译器确保写入操作完成
        }
    }

    deinit {
        NSLog("ViewController deinit 被调用")
        isRenderingActive = false
        
        // 停止所有渲染机制
        stopCVDisplayLink()
        stopBackupTimer()
        
        // 彻底清理 MTKView
        metalView?.isPaused = true
        metalView?.delegate = nil
        
        // 清理Metal资源
        earthVertexBuffer = nil
        earthIndexBuffer = nil
        earthUniformBuffer = nil
        earthTexture = nil
        renderPipelineState = nil
        depthStencilState = nil
        commandQueue = nil
        device = nil
        
        NSLog("✅ ViewController资源清理完成")
    }
}

// MARK: - MTKViewDelegate
extension ViewController {
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        // 处理视图大小变化
        view.setNeedsDisplay(view.bounds) // 强制在drawable大小变化时重绘
    }

    func draw(in view: MTKView) {
        // 首次执行时打印重要信息
        if frameCount == 0 {
            NSLog("🎬 首次 draw(in view:) 调用")
            NSLog("   - view.isPaused: \(view.isPaused)")
            NSLog("   - view.enableSetNeedsDisplay: \(view.enableSetNeedsDisplay)")
            NSLog("   - view.preferredFramesPerSecond: \(view.preferredFramesPerSecond)")
            NSLog("   - isRenderingActive: \(isRenderingActive)")
        }
        
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor else {
            NSLog("⚠️ draw(in view:) - 无法获取drawable或renderPassDescriptor，Frame: \(frameCount)")
            return
        }

        // 重要：配置CAMetalLayer为false以启用流畅动画
        if let metalLayer = view.layer as? CAMetalLayer {
            metalLayer.presentsWithTransaction = false  // 修改为false以避免阻塞
            metalLayer.displaySyncEnabled = true        // 启用垂直同步
        }

        let currentTime = Float(CACurrentMediaTime() - startTime)
        frameCount += 1

        // 添加调试输出来确认时间变化 - 每60帧打印一次
        if frameCount % 60 == 0 {  
            NSLog("🎬 Frame \(frameCount): currentTime = \(currentTime), rotation = \(currentTime * 1.0)")
        }
        
        // 每300帧（5秒）打印一次详细状态
        if frameCount % 300 == 0 {
            NSLog("📊 渲染状态检查:")
            NSLog("   - isRenderingActive: \(isRenderingActive)")
            NSLog("   - view.isPaused: \(view.isPaused)")
            NSLog("   - view.enableSetNeedsDisplay: \(view.enableSetNeedsDisplay)")
            NSLog("   - view.delegate: \(view.delegate != nil ? "✅" : "❌")")
        }

        // 更新统一变量
        updateUniforms(time: currentTime)

        // 创建命令缓冲区
        guard let commandBuffer = commandQueue.makeCommandBuffer() else { return }
        commandBuffer.label = "Earth Rendering"  // 添加标签便于调试

        // 创建渲染编码器
        guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else { return }
        renderEncoder.label = "Earth Render Encoder"

        renderEncoder.setRenderPipelineState(renderPipelineState)
        renderEncoder.setDepthStencilState(depthStencilState)

        // 使用地球专用的缓冲区 - 独立的uniform buffer
        renderEncoder.setVertexBuffer(earthVertexBuffer, offset: 0, index: 0)
        renderEncoder.setVertexBuffer(earthUniformBuffer, offset: 0, index: 1)  // 地球专用uniform buffer
        renderEncoder.setFragmentBuffer(earthUniformBuffer, offset: 0, index: 1)  // 地球专用uniform buffer
        renderEncoder.setFragmentTexture(earthTexture, index: 0)

        renderEncoder.drawIndexedPrimitives(type: .triangle,
                                          indexCount: indices.count,
                                          indexType: .uint16,
                                          indexBuffer: earthIndexBuffer,  // 地球专用索引缓冲区
                                          indexBufferOffset: 0)

        renderEncoder.endEncoding()
        
        // 提交命令缓冲区并呈现
        commandBuffer.present(drawable)
        commandBuffer.commit()
    }
}

// MARK: - CVDisplayLink 管理方法
extension ViewController {
    private func startCVDisplayLink() {
        guard displayLink == nil else { return }
        
        var displayLinkRef: CVDisplayLink?
        let result = CVDisplayLinkCreateWithActiveCGDisplays(&displayLinkRef)
        
        guard result == kCVReturnSuccess, let displayLink = displayLinkRef else {
            NSLog("❌ 无法创建 CVDisplayLink")
            return
        }
        
        self.displayLink = displayLink
        
        // 设置输出回调
        let callback: CVDisplayLinkOutputCallback = { (displayLink, inNow, inOutputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
            let viewController = Unmanaged<ViewController>.fromOpaque(displayLinkContext!).takeUnretainedValue()
            
            DispatchQueue.main.async {
                viewController.cvDisplayLinkCallback()
            }
            
            return kCVReturnSuccess
        }
        
        CVDisplayLinkSetOutputCallback(displayLink, callback, UnsafeMutableRawPointer(Unmanaged.passUnretained(self).toOpaque()))
        
        let startResult = CVDisplayLinkStart(displayLink)
        if startResult == kCVReturnSuccess {
            NSLog("✅ CVDisplayLink 启动成功")
        } else {
            NSLog("❌ CVDisplayLink 启动失败: \(startResult)")
        }
    }
    
    private func stopCVDisplayLink() {
        guard let displayLink = displayLink else { return }
        
        CVDisplayLinkStop(displayLink)
        self.displayLink = nil
        NSLog("🛑 CVDisplayLink 已停止")
    }
    
    private func cvDisplayLinkCallback() {
        // CVDisplayLink 回调，当 MTKView 自动渲染失效时强制触发渲染
        guard isRenderingActive else { return }
        
        // 避免重复调用 - 确保在主线程上执行
        DispatchQueue.main.async { [weak self] in
            guard let self = self, self.isRenderingActive else { return }
            
            // 手动触发 MTKView 渲染
            self.metalView.draw()
            
            // 每60帧打印一次状态（约每秒一次）
            if self.frameCount % 60 == 0 {
                NSLog("🔄 CVDisplayLink 强制渲染 Frame \(self.frameCount)")
            }
        }
    }
    
    // MARK: - 备用渲染机制
    private func startBackupTimer() {
        // 如果已有计时器，先停止避免重复
        stopBackupTimer()
        
        NSLog("🚨 启动永久备用渲染计时器 - 作为主要渲染机制")
        
        // 确保MTKView设置完全正确以接收渲染
        self.metalView.delegate = self
        self.metalView.isPaused = false
        self.metalView.enableSetNeedsDisplay = false
        
        // 创建更高优先级的备用计时器，保持精确的60fps (1/60 ≈ 0.0167秒)
        let timer = Timer(timeInterval: 1.0/60.0, repeats: true) { [weak self] timer in
            guard let self = self, self.isRenderingActive else {
                timer.invalidate()
                return
            }
            
            // 直接在计时器回调中触发渲染，避免额外的RunLoop延迟
            // 每次渲染前检查delegate是否还存在
            if self.metalView.delegate == nil {
                NSLog("🔧 备用计时器修复丢失的delegate")
                self.metalView.delegate = self
            }
            self.metalView.draw()
            
            // 每60次（约1秒）打印状态
            if self.frameCount % 60 == 0 {
                NSLog("🔄 备用计时器渲染 Frame \(self.frameCount)")
            }
        }
        
        // 安排高优先级计时器，确保即使在UI事件处理期间也能保持稳定渲染
        backupTimer = timer
        RunLoop.main.add(timer, forMode: .common)
        RunLoop.main.add(timer, forMode: .default)
        
        // 立即执行一次强制渲染以确保连续性
        self.metalView.draw()
        
        // 简化监控机制：使用单一的高可靠性检查点
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            guard let self = self, self.isRenderingActive else { return }
            
            // 记录当前帧计数
            let lastFrameCount = self.frameCount
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                guard let self = self, self.isRenderingActive else { return }
                let newFrameCount = self.frameCount
                
                // 检查渲染性能
                let framesPerSecond = newFrameCount - lastFrameCount
                if framesPerSecond < 30 {
                    NSLog("⚠️ 检测到渲染性能不佳(\(framesPerSecond)帧/秒)，重启计时器")
                    self.stopBackupTimer()
                    self.startBackupTimer()
                } else {
                    NSLog("✅ 备用计时器运行正常: \(framesPerSecond)帧/秒")
                }
                
                // 创建周期性监控，但间隔更长，减少系统负担
                if self.isRenderingActive {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) { [weak self] in
                        guard let self = self else { return }
                        if self.backupTimer == nil && self.isRenderingActive {
                            NSLog("⚠️ 备用计时器已丢失，重新启动")
                            self.startBackupTimer()
                        }
                    }
                }
            }
        }
    }
    
    private func stopBackupTimer() {
        if let timer = backupTimer {
            timer.invalidate()
            backupTimer = nil
            NSLog("🛑 备用计时器已停止")
        }
    }
    
    // checkAndRestoreMTKViewRendering() 方法已被移除，
    // 因为我们现在使用简化的渲染机制，所有检查已集成到startBackupTimer方法中
}

