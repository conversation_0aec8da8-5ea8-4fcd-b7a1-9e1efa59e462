{"mcpServers": {"mcp-filesystem": {"command": "uv", "args": ["--directory", "/Users/<USER>/Downloads/mcp-filesystem", "run", "run_server.py", "/Users/<USER>/Downloads"], "env": {"NODE_ENV": "production"}, "alwaysAllow": ["list_allowed_directories", "read_file", "read_multiple_files", "write_file", "create_directory", "list_directory", "move_file", "get_file_info", "edit_file", "head_file", "tail_file", "search_files", "directory_tree", "calculate_directory_size", "find_duplicate_files", "compare_files", "find_large_files", "find_empty_directories", "grep_files", "read_file_lines", "edit_file_at_line"]}, "exa": {"command": "exa-mcp-server", "args": ["--tools=web_search_exa,research_paper_search,company_research,crawling,competitor_finder,linkedin_search,wikipedia_search_exa,github_search"], "env": {"EXA_API_KEY": "7c9f95f7-e3c1-46f7-9e0b-3b90e9fede9f"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {}}, "open-memory": {"command": "mcp-knowledge-graph", "args": ["--memory-path", "/Users/<USER>/Documents/cursor_memory/project_memory.jsonl"], "env": {"MEM0_API_KEY": "m0-jll2t63hnZeB16eS3kSwRhBnvBCSgN5HkeBawo5M"}}, "think-tool": {"command": "npx", "args": ["-y", "@cgize/mcp-think-tool"], "env": {}}}}